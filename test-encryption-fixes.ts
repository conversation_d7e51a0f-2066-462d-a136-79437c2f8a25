#!/usr/bin/env ts-node

/**
 * Test Script for Encryption Fixes
 * 
 * This script tests the fixed encryption functions and provides debugging
 * information to help resolve the ASN.1 "Too few bytes to read" error.
 */

import { payloadEncryptionFactory } from './src/utils/encryption';

// Your test payload (same structure as in the logs)
const TEST_PAYLOAD = {
  pin: "1234",
  cardId: "****************", 
  cardType: "VISA",
  otp: "123456"
};

// You'll need to replace this with your actual certificate
const TEST_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
QIDAQAB
-----END CERTIFICATE-----`;

/**
 * Compare the problematic result with our fixed version
 */
function analyzeResults(originalResult: string, fixedResult: string) {
  console.log('\n📊 COMPARISON ANALYSIS');
  console.log('=' .repeat(60));
  
  console.log('\n🔴 ORIGINAL (Problematic) Result:');
  console.log(`Length: ${originalResult.length} characters`);
  console.log(`Data: ${originalResult}`);
  
  console.log('\n🟢 FIXED Result:');
  console.log(`Length: ${fixedResult.length} characters`);
  console.log(`Data: ${fixedResult.substring(0, 100)}...`);
  
  console.log('\n🔍 Key Differences:');
  if (fixedResult.length !== originalResult.length) {
    console.log(`✅ Length changed: ${originalResult.length} → ${fixedResult.length}`);
  }
  
  if (fixedResult !== originalResult) {
    console.log('✅ Content is different (expected - fixes applied)');
  } else {
    console.log('⚠️ Content is identical (may need further investigation)');
  }
}

/**
 * Test both encryption approaches
 */
async function testEncryptionApproaches() {
  console.log('🧪 TESTING ENCRYPTION FIXES');
  console.log('=' .repeat(60));
  
  console.log('\n📋 Test Payload:');
  console.log(JSON.stringify(TEST_PAYLOAD, null, 2));
  
  console.log('\n🔧 Test 1: Fixed PKCS#7 Envelope Approach');
  console.log('-' .repeat(50));
  
  try {
    const pkcs7Result = await payloadEncryptionFactory(
      TEST_PAYLOAD, 
      TEST_CERTIFICATE, 
      true, // ignore expiration
      false // use PKCS#7 envelope
    );
    
    if (pkcs7Result) {
      console.log('✅ PKCS#7 encryption successful');
      console.log(`📏 Length: ${pkcs7Result.length} characters`);
      console.log(`🔤 First 100 chars: ${pkcs7Result.substring(0, 100)}...`);
      
      // Compare with the problematic result from your logs
      const PROBLEMATIC_RESULT = "MDEGCSrChkjChsO3DQEHA8KgJDAiAgEAMRAwDgIBAAQJVGVzdElzc3VlMAsGCSrChkjChsO3DQEHAQ==";
      analyzeResults(PROBLEMATIC_RESULT, pkcs7Result);
      
    } else {
      console.log('❌ PKCS#7 encryption failed');
    }
  } catch (error) {
    console.error('❌ PKCS#7 encryption error:', error);
  }
  
  console.log('\n🔧 Test 2: Simple RSA Approach (No PKCS#7)');
  console.log('-' .repeat(50));
  
  try {
    const simpleResult = await payloadEncryptionFactory(
      TEST_PAYLOAD, 
      TEST_CERTIFICATE, 
      true, // ignore expiration
      true  // use simple RSA (no PKCS#7)
    );
    
    if (simpleResult) {
      console.log('✅ Simple RSA encryption successful');
      console.log(`📏 Length: ${simpleResult.length} characters`);
      console.log(`🔤 First 100 chars: ${simpleResult.substring(0, 100)}...`);
    } else {
      console.log('❌ Simple RSA encryption failed');
    }
  } catch (error) {
    console.error('❌ Simple RSA encryption error:', error);
  }
}

/**
 * Provide debugging recommendations
 */
function provideRecommendations() {
  console.log('\n💡 DEBUGGING RECOMMENDATIONS');
  console.log('=' .repeat(60));
  
  console.log('\n1. 🔧 IMMEDIATE FIXES TO TRY:');
  console.log('   • Use the simple RSA approach first (no PKCS#7 envelope)');
  console.log('   • Test with your actual certificate');
  console.log('   • Verify the backend accepts simple RSA encrypted data');
  
  console.log('\n2. 🔍 ROOT CAUSE ANALYSIS:');
  console.log('   • The original error was caused by UTF-8 encoding corruption');
  console.log('   • OID bytes were corrupted: C2, C3 bytes indicate UTF-8 multi-byte sequences');
  console.log('   • ASN.1 structure was malformed due to encoding issues');
  
  console.log('\n3. 📋 BACKEND COMPATIBILITY:');
  console.log('   • Check if backend requires PKCS#7 EnvelopedData format');
  console.log('   • If yes, use the fixed PKCS#7 envelope function');
  console.log('   • If no, use simple RSA encryption for better compatibility');
  
  console.log('\n4. 🧪 TESTING STEPS:');
  console.log('   • Replace TEST_CERTIFICATE with your actual certificate');
  console.log('   • Run this script to test both approaches');
  console.log('   • Test the results with your backend API');
  console.log('   • Monitor for ASN.1 parsing errors');
  
  console.log('\n5. 🔧 INTEGRATION IN YOUR CODE:');
  console.log('   • Update your CardManagement.ts to use the new parameters:');
  console.log('     const encrypted = await payloadEncryptionFactory(body, key, true, true);');
  console.log('   • The last parameter (true) enables simple RSA mode for debugging');
  console.log('   • Set it to false once you confirm PKCS#7 envelope works');
}

/**
 * Main test function
 */
async function main() {
  console.log('🔐 ENCRYPTION ERROR DEBUGGING TOOL');
  console.log('=' .repeat(60));
  console.log('This tool helps debug the "Too few bytes to read ASN.1 value" error');
  console.log();
  
  try {
    await testEncryptionApproaches();
    provideRecommendations();
    
    console.log('\n✅ TESTING COMPLETED');
    console.log('Review the results above and follow the recommendations.');
    
  } catch (error) {
    console.error('\n❌ TESTING FAILED:', error);
    console.log('\n🔧 TROUBLESHOOTING:');
    console.log('1. Make sure you have the correct certificate');
    console.log('2. Check that all dependencies are installed');
    console.log('3. Verify the certificate format is valid PEM');
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

export { testEncryptionApproaches, analyzeResults };
