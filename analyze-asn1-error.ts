#!/usr/bin/env ts-node

/**
 * ASN.1 Analysis Tool for Debugging Encryption/Decryption Errors
 * 
 * This script analyzes the encrypted payload that's causing the 
 * "Too few bytes to read ASN.1 value" error and provides detailed
 * information about the ASN.1 structure.
 */

// The encrypted result from your logs
const ENCRYPTED_RESULT = "MDEGCSrChkjChsO3DQEHA8KgJDAiAgEAMRAwDgIBAAQJVGVzdElzc3VlMAsGCSrChkjChsO3DQEHAQ==";

/**
 * Convert base64 to hex string for analysis
 */
function base64ToHex(base64: string): string {
  try {
    const binary = atob(base64);
    let hex = '';
    for (let i = 0; i < binary.length; i++) {
      const byte = binary.charCodeAt(i);
      hex += byte.toString(16).padStart(2, '0').toUpperCase();
    }
    return hex;
  } catch (error) {
    console.error('Failed to decode base64:', error);
    return '';
  }
}

/**
 * Convert base64 to byte array for detailed analysis
 */
function base64ToBytes(base64: string): number[] {
  try {
    const binary = atob(base64);
    const bytes: number[] = [];
    for (let i = 0; i < binary.length; i++) {
      bytes.push(binary.charCodeAt(i));
    }
    return bytes;
  } catch (error) {
    console.error('Failed to decode base64 to bytes:', error);
    return [];
  }
}

/**
 * Parse ASN.1 length encoding
 */
function parseASN1Length(bytes: number[], offset: number): { length: number, bytesUsed: number } {
  if (offset >= bytes.length) {
    throw new Error('Offset beyond byte array length');
  }

  const firstByte = bytes[offset];
  
  if ((firstByte & 0x80) === 0) {
    // Short form: length is in the first byte
    return { length: firstByte, bytesUsed: 1 };
  } else {
    // Long form: first byte indicates how many additional bytes contain the length
    const lengthBytes = firstByte & 0x7F;
    
    if (lengthBytes === 0) {
      throw new Error('Indefinite length not supported');
    }
    
    if (offset + lengthBytes >= bytes.length) {
      throw new Error('Not enough bytes for length encoding');
    }
    
    let length = 0;
    for (let i = 1; i <= lengthBytes; i++) {
      length = (length << 8) | bytes[offset + i];
    }
    
    return { length, bytesUsed: lengthBytes + 1 };
  }
}

/**
 * Parse ASN.1 TLV (Tag-Length-Value) structure
 */
function parseASN1TLV(bytes: number[], offset: number = 0, depth: number = 0): any {
  const indent = '  '.repeat(depth);
  
  if (offset >= bytes.length) {
    console.log(`${indent}ERROR: Offset ${offset} beyond byte array length ${bytes.length}`);
    return null;
  }

  const tag = bytes[offset];
  console.log(`${indent}Tag: 0x${tag.toString(16).padStart(2, '0').toUpperCase()} (${getTagDescription(tag)})`);
  
  try {
    const lengthInfo = parseASN1Length(bytes, offset + 1);
    console.log(`${indent}Length: ${lengthInfo.length} bytes (encoded in ${lengthInfo.bytesUsed} bytes)`);
    
    const valueOffset = offset + 1 + lengthInfo.bytesUsed;
    const valueEnd = valueOffset + lengthInfo.length;
    
    if (valueEnd > bytes.length) {
      console.log(`${indent}ERROR: Value extends beyond available data (need ${valueEnd}, have ${bytes.length})`);
      return null;
    }
    
    const value = bytes.slice(valueOffset, valueEnd);
    console.log(`${indent}Value: ${value.map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' ')}`);
    
    // If it's a constructed type (SEQUENCE, SET, etc.), parse recursively
    if ((tag & 0x20) !== 0) {
      console.log(`${indent}Constructed type - parsing contents:`);
      let childOffset = valueOffset;
      let childIndex = 0;
      
      while (childOffset < valueEnd) {
        console.log(`${indent}  Child ${childIndex}:`);
        const childResult = parseASN1TLV(bytes, childOffset, depth + 2);
        if (!childResult) break;
        childOffset = childResult.nextOffset;
        childIndex++;
      }
    } else if (tag === 0x06) {
      // OBJECT IDENTIFIER - decode OID
      const oidString = decodeOID(value);
      console.log(`${indent}OID: ${oidString}`);
    } else if (tag === 0x02) {
      // INTEGER
      const intValue = decodeInteger(value);
      console.log(`${indent}Integer value: ${intValue}`);
    } else if (tag === 0x04) {
      // OCTET STRING
      const stringValue = decodeOctetString(value);
      console.log(`${indent}Octet string: ${stringValue}`);
    }
    
    return {
      tag,
      length: lengthInfo.length,
      value,
      nextOffset: valueEnd
    };
    
  } catch (error) {
    console.log(`${indent}ERROR parsing length: ${error.message}`);
    return null;
  }
}

/**
 * Get human-readable description of ASN.1 tag
 */
function getTagDescription(tag: number): string {
  const constructed = (tag & 0x20) !== 0;
  const tagNumber = tag & 0x1F;
  
  const descriptions: { [key: number]: string } = {
    0x01: 'BOOLEAN',
    0x02: 'INTEGER',
    0x03: 'BIT STRING',
    0x04: 'OCTET STRING',
    0x05: 'NULL',
    0x06: 'OBJECT IDENTIFIER',
    0x0C: 'UTF8String',
    0x13: 'PrintableString',
    0x16: 'IA5String',
    0x17: 'UTCTime',
    0x18: 'GeneralizedTime',
    0x30: 'SEQUENCE',
    0x31: 'SET',
    0xA0: 'CONTEXT [0]',
    0xA1: 'CONTEXT [1]',
    0xA2: 'CONTEXT [2]',
    0xA3: 'CONTEXT [3]',
    0x80: 'CONTEXT [0] IMPLICIT',
    0x81: 'CONTEXT [1] IMPLICIT',
    0x82: 'CONTEXT [2] IMPLICIT',
    0x83: 'CONTEXT [3] IMPLICIT'
  };
  
  const desc = descriptions[tag] || `Unknown tag ${tagNumber}`;
  return constructed ? `${desc} (constructed)` : desc;
}

/**
 * Decode OBJECT IDENTIFIER
 */
function decodeOID(bytes: number[]): string {
  if (bytes.length === 0) return '';
  
  const oid: number[] = [];
  
  // First byte encodes first two arcs
  const firstByte = bytes[0];
  oid.push(Math.floor(firstByte / 40));
  oid.push(firstByte % 40);
  
  // Remaining bytes encode subsequent arcs
  let i = 1;
  while (i < bytes.length) {
    let value = 0;
    let byte;
    do {
      if (i >= bytes.length) break;
      byte = bytes[i++];
      value = (value << 7) | (byte & 0x7F);
    } while ((byte & 0x80) !== 0);
    oid.push(value);
  }
  
  return oid.join('.');
}

/**
 * Decode INTEGER
 */
function decodeInteger(bytes: number[]): number {
  if (bytes.length === 0) return 0;
  
  let value = 0;
  for (const byte of bytes) {
    value = (value << 8) | byte;
  }
  
  return value;
}

/**
 * Decode OCTET STRING as text if possible
 */
function decodeOctetString(bytes: number[]): string {
  try {
    // Try to decode as UTF-8 text
    const text = String.fromCharCode(...bytes);
    if (/^[\x20-\x7E]*$/.test(text)) {
      return `"${text}"`;
    }
  } catch (error) {
    // Fall back to hex representation
  }
  
  return bytes.map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' ');
}

/**
 * Main analysis function
 */
function analyzeEncryptedPayload() {
  console.log('🔍 ASN.1 Analysis Tool for Encryption/Decryption Error');
  console.log('=' .repeat(60));
  console.log();
  
  console.log('📋 Encrypted Result from Logs:');
  console.log(ENCRYPTED_RESULT);
  console.log();
  
  console.log('🔢 Hex Representation:');
  const hexData = base64ToHex(ENCRYPTED_RESULT);
  console.log(hexData);
  console.log();
  
  console.log('📏 Data Length:');
  const bytes = base64ToBytes(ENCRYPTED_RESULT);
  console.log(`${bytes.length} bytes`);
  console.log();
  
  console.log('🏗️ ASN.1 Structure Analysis:');
  console.log('-'.repeat(40));
  
  try {
    parseASN1TLV(bytes);
  } catch (error) {
    console.error('Failed to parse ASN.1 structure:', error);
  }
  
  console.log();
  console.log('🔍 Potential Issues:');
  console.log('-'.repeat(40));
  
  // Check for common issues
  if (bytes.length < 10) {
    console.log('❌ Data is very short - may be incomplete');
  }
  
  if (bytes[0] !== 0x30) {
    console.log('❌ Does not start with SEQUENCE tag (0x30) - invalid PKCS#7 structure');
  }
  
  // Check if the structure looks like a proper PKCS#7 EnvelopedData
  console.log();
  console.log('📋 Expected PKCS#7 EnvelopedData Structure:');
  console.log('SEQUENCE {');
  console.log('  OBJECT IDENTIFIER envelopedData (1.2.840.113549.1.7.3)');
  console.log('  [0] EXPLICIT SEQUENCE {');
  console.log('    INTEGER version');
  console.log('    SET OF RecipientInfo');
  console.log('    EncryptedContentInfo');
  console.log('  }');
  console.log('}');
}

// Run the analysis
analyzeEncryptedPayload();
