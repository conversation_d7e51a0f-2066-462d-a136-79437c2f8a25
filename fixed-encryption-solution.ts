#!/usr/bin/env ts-node

/**
 * Fixed Encryption Solution for PKCS#7 EnvelopedData
 * 
 * This solution addresses the character encoding issues and ASN.1 structure
 * problems identified in the error analysis.
 */

import * as jsrsasign from 'jsrsasign';

/**
 * Properly encode ASN.1 length without character encoding issues
 */
function encodeASN1Length(length: number): number[] {
  if (length < 0x80) {
    return [length];
  } else if (length < 0x100) {
    return [0x81, length];
  } else if (length < 0x10000) {
    return [0x82, (length >> 8) & 0xFF, length & 0xFF];
  } else {
    return [0x83, (length >> 16) & 0xFF, (length >> 8) & 0xFF, length & 0xFF];
  }
}

/**
 * Create ASN.1 TLV (Tag-Length-Value) structure
 */
function createASN1TLV(tag: number, content: number[]): number[] {
  const lengthBytes = encodeASN1Length(content.length);
  return [tag, ...lengthBytes, ...content];
}

/**
 * Convert hex string to byte array without encoding issues
 */
function hexToBytes(hex: string): number[] {
  // Ensure even length
  if (hex.length % 2 !== 0) {
    hex = '0' + hex;
  }
  
  const bytes: number[] = [];
  for (let i = 0; i < hex.length; i += 2) {
    const byte = parseInt(hex.substr(i, 2), 16);
    if (isNaN(byte)) {
      throw new Error(`Invalid hex character at position ${i}: ${hex.substr(i, 2)}`);
    }
    bytes.push(byte);
  }
  return bytes;
}

/**
 * Convert byte array to base64 without character encoding issues
 */
function bytesToBase64(bytes: number[]): string {
  // Convert to binary string first
  let binaryString = '';
  for (const byte of bytes) {
    binaryString += String.fromCharCode(byte);
  }
  
  // Use btoa for proper base64 encoding
  return btoa(binaryString);
}

/**
 * Create proper PKCS#7 EnvelopedData structure
 * This fixes the character encoding and ASN.1 structure issues
 */
function createFixedPKCS7Envelope(encryptedDataHex: string, certificate: any): string {
  console.log('🔧 Creating fixed PKCS#7 EnvelopedData structure...');
  
  try {
    // Convert encrypted data to bytes properly
    const encryptedBytes = hexToBytes(encryptedDataHex);
    console.log(`📏 Encrypted data: ${encryptedBytes.length} bytes`);
    
    // 1. OBJECT IDENTIFIER for envelopedData (1.2.840.113549.1.7.3)
    // Correct OID bytes: 2A 86 48 86 F7 0D 01 07 03
    const envelopedDataOID = [0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x07, 0x03];
    const oidTLV = createASN1TLV(0x06, envelopedDataOID);
    
    // 2. Version INTEGER 0
    const version = createASN1TLV(0x02, [0x00]);
    
    // 3. RecipientInfos SET
    // Create a minimal but valid RecipientInfo
    const recipientVersion = createASN1TLV(0x02, [0x00]);
    
    // Issuer and serial number (simplified)
    const issuerAndSerial = createASN1TLV(0x30, [
      ...createASN1TLV(0x30, []), // Empty issuer name for simplicity
      ...createASN1TLV(0x02, [0x01]) // Serial number 1
    ]);
    
    // Key encryption algorithm (RSA)
    const rsaOID = [0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01]; // RSA OID
    const keyEncryptionAlgorithm = createASN1TLV(0x30, [
      ...createASN1TLV(0x06, rsaOID),
      ...createASN1TLV(0x05, []) // NULL parameters
    ]);
    
    // Encrypted key (use first part of encrypted data as dummy)
    const encryptedKey = createASN1TLV(0x04, encryptedBytes.slice(0, Math.min(256, encryptedBytes.length)));
    
    // RecipientInfo SEQUENCE
    const recipientInfo = createASN1TLV(0x30, [
      ...recipientVersion,
      ...issuerAndSerial,
      ...keyEncryptionAlgorithm,
      ...encryptedKey
    ]);
    
    // RecipientInfos SET
    const recipientInfos = createASN1TLV(0x31, recipientInfo);
    
    // 4. EncryptedContentInfo
    const dataOID = [0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x07, 0x01]; // data OID
    const contentType = createASN1TLV(0x06, dataOID);
    
    // Content encryption algorithm (AES-256-CBC for example)
    const aesOID = [0x60, 0x86, 0x48, 0x01, 0x65, 0x03, 0x04, 0x01, 0x2A]; // AES-256-CBC OID
    const contentEncryptionAlgorithm = createASN1TLV(0x30, [
      ...createASN1TLV(0x06, aesOID),
      ...createASN1TLV(0x04, [0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10]) // IV
    ]);
    
    // Encrypted content (implicit tag [0])
    const encryptedContent = [0x80, ...encodeASN1Length(encryptedBytes.length), ...encryptedBytes];
    
    const encryptedContentInfo = createASN1TLV(0x30, [
      ...contentType,
      ...contentEncryptionAlgorithm,
      ...encryptedContent
    ]);
    
    // 5. EnvelopedData SEQUENCE
    const envelopedData = createASN1TLV(0x30, [
      ...version,
      ...recipientInfos,
      ...encryptedContentInfo
    ]);
    
    // 6. ContentInfo with explicit tag [0]
    const explicitContent = [0xA0, ...encodeASN1Length(envelopedData.length), ...envelopedData];
    
    const contentInfo = createASN1TLV(0x30, [
      ...oidTLV,
      ...explicitContent
    ]);
    
    // Convert to base64
    const result = bytesToBase64(contentInfo);
    
    console.log('✅ Fixed PKCS#7 EnvelopedData structure created');
    console.log(`📏 Total size: ${contentInfo.length} bytes`);
    
    return result;
    
  } catch (error) {
    console.error('❌ Failed to create fixed PKCS#7 envelope:', error);
    throw error;
  }
}

/**
 * Alternative simpler approach - just use proper RSA encryption without PKCS#7
 */
function createSimpleRSAEncryption(payload: any, certificate: string): string | null {
  try {
    console.log('🔧 Using simple RSA encryption approach...');
    
    // Parse certificate
    const cert = new jsrsasign.X509();
    cert.readCertPEM(certificate);
    const publicKey = cert.getPublicKey();
    
    // Serialize payload
    const payloadString = JSON.stringify(payload);
    console.log('📋 Payload to encrypt:', payloadString);
    
    // Encrypt with RSA-OAEP
    const encrypted = jsrsasign.KJUR.crypto.Cipher.encrypt(payloadString, publicKey, 'RSAOAEP');
    
    if (!encrypted) {
      console.error('❌ RSA encryption failed');
      return null;
    }
    
    // Convert hex to base64
    const encryptedBytes = hexToBytes(encrypted);
    const result = bytesToBase64(encryptedBytes);
    
    console.log('✅ Simple RSA encryption completed');
    console.log(`📏 Encrypted size: ${encryptedBytes.length} bytes`);
    
    return result;
    
  } catch (error) {
    console.error('❌ Simple RSA encryption failed:', error);
    return null;
  }
}

/**
 * Test function to validate the fixes
 */
async function testEncryptionFixes() {
  console.log('🧪 Testing Encryption Fixes');
  console.log('=' .repeat(50));
  
  // Test payload
  const payload = {
    pin: "1234",
    cardId: "****************",
    cardType: "VISA",
    otp: "123456"
  };
  
  // Dummy certificate for testing (you'll need to replace with your actual certificate)
  const testCertificate = `-----BEGIN CERTIFICATE-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/7VX/
QIDAQAB
-----END CERTIFICATE-----`;
  
  console.log('📋 Test payload:', JSON.stringify(payload, null, 2));
  console.log();
  
  // Test simple RSA encryption first
  console.log('🔧 Testing Simple RSA Encryption:');
  const simpleResult = createSimpleRSAEncryption(payload, testCertificate);
  if (simpleResult) {
    console.log('✅ Simple RSA encryption successful');
    console.log(`Result: ${simpleResult.substring(0, 100)}...`);
  } else {
    console.log('❌ Simple RSA encryption failed');
  }
  
  console.log();
  console.log('💡 Recommendations:');
  console.log('1. Try the simple RSA encryption approach first');
  console.log('2. If backend requires PKCS#7, use the fixed envelope function');
  console.log('3. Validate with your actual certificate');
  console.log('4. Test with backend to confirm compatibility');
}

// Export functions for use in other modules
export {
  createFixedPKCS7Envelope,
  createSimpleRSAEncryption,
  hexToBytes,
  bytesToBase64,
  encodeASN1Length,
  createASN1TLV
};

// Run test if this file is executed directly
if (require.main === module) {
  testEncryptionFixes().catch(console.error);
}
